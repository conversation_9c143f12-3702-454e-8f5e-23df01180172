'use client'
import React, { useState, useEffect } from 'react'
import {
    Box,
    Button,
    Card,
    Heading,
    Text,
    VStack,
    HStack,
    Input,
    Textarea,
    Image,
    Badge,
    Separator,
    Grid,
    GridItem,
} from '@chakra-ui/react'
import { Alert, AlertIcon, AlertTitle, AlertDescription } from '@/components/ui/alert'
// import { Select } from '@/components/ui/select'
import { FormControl, FormLabel, FormErrorMessage } from '@/components/ui/form'
import { FaTrophy, FaGavel, FaClock, FaCheckCircle, FaExclamationTriangle } from 'react-icons/fa'
import { format } from 'date-fns'

import { Controller, useForm } from 'react-hook-form'
import { useProductByIdQuery } from '@/services/useProductQuery'
import { useBuyNowMutation } from '@/services/useCheckoutQuery'

import { toaster } from '@/components/ui/toaster'
import { useRouter, useSearchParams } from 'next/navigation'
import FormSelectField, { SelectOption } from '@/components/ui/form/FormSelectField'
import PaymentMethodSelector from '@/components/payment/PaymentMethodSelector'
import ShippingOptionsSelector from '@/components/shipping/ShippingOptionsSelector'
import { useCurrencyLanguage } from '@/contexts/CurrencyLanguageContext'
import countries from '@/datas/country.json';
import { SingleValue } from 'react-select'
import { CartItem, useCartQuery } from '@/services/useCartQuery'
import { useSession } from 'next-auth/react'
import { useCreateInvoiceMutation, useCreatePaymentMutation, openPaymentUrl } from '@/services/usePaymentQuery'
import { useBidDetailQuery, useUserBidsQuery } from '@/services/useBiddingQuery'
import {
    useAuctionWinnerDetailsQuery,
    useAuctionWinnerByProductQuery,
    formatPaymentDeadline,
    calculateTimeRemaining
} from '@/services/useAuctionPaymentQuery'
import { useUserOrdersQuery } from '@/services/useOrderTrackingQuery'


interface BuyNowFormData {
    // Shipping Address
    name: string
    address: string
    city: string
    provinceRegion: string
    zipCode: string
    country: string

    // Payment Information
    paymentMethod: string
    selectedPaymentMethod?: string
    ewalletType?: string
    paymentChannel?: string
    bankCode?: string
    retailOutletName?: string
    shippingOption?: any
    currency: 'USD' | 'IDR'

    notes?: string
}

// Auction Winner Banner Component
const AuctionWinnerBanner = ({ bidData, product }: { bidData: any; product: any }) => {
    if (!bidData || !product) return null

    return (
        <Box p={6} bg="green.50" border="2px solid" borderColor="green.200" borderRadius="lg">
            <VStack gap={4} align="stretch">
                <HStack gap={3} justify="center">
                    <Box color="green.500" fontSize="2xl">
                        <FaTrophy />
                    </Box>
                    <Heading size="lg" color="green.700" textAlign="center">
                        🎉 Congratulations! You Won This Auction!
                    </Heading>
                </HStack>

                <Grid templateColumns={{ base: '1fr', md: '1fr 1fr 1fr' }} gap={4}>
                    <VStack gap={1}>
                        <Text fontSize="sm" color="gray.600">Your Winning Bid</Text>
                        <Text fontSize="xl" fontWeight="bold" color="green.600">
                            ${bidData.amount.toLocaleString()}
                        </Text>
                    </VStack>
                    <VStack gap={1}>
                        <Text fontSize="sm" color="gray.600">Auction Ended</Text>
                        <Text fontSize="md" fontWeight="semibold" color="gray.700">
                            {product.auctionEndDate ? new Date(product.auctionEndDate).toLocaleDateString() : 'Recently'}
                        </Text>
                    </VStack>
                    <VStack gap={1}>
                        <Text fontSize="sm" color="gray.600">Item</Text>
                        <Text fontSize="md" fontWeight="semibold" color="gray.700" textAlign="center">
                            {product.itemName}
                        </Text>
                    </VStack>
                </Grid>

                <Box p={3} bg="blue.50" border="1px solid" borderColor="blue.200" borderRadius="md">
                    <HStack gap={2}>
                        <Box color="blue.500">
                            <FaCheckCircle />
                        </Box>
                        <VStack align="start" gap={1}>
                            <Text fontSize="sm" fontWeight="semibold" color="blue.700">Complete Your Purchase</Text>
                            <Text fontSize="xs" color="blue.600">
                                Please complete the payment process below to secure your winning item.
                                You have 3 days from the auction end date to complete your purchase.
                            </Text>
                        </VStack>
                    </HStack>
                </Box>
            </VStack>
        </Box>
    )
}

// Enhanced Payment Deadline Warning Component
const PaymentDeadlineWarning = ({ auctionWinnerDetails }: { auctionWinnerDetails?: any }) => {
    if (!auctionWinnerDetails?.paymentDeadline) return null

    const timeRemaining = calculateTimeRemaining(auctionWinnerDetails.paymentDeadline)

    if (timeRemaining.isOverdue) {
        return (
            <Box p={4} bg="red.50" border="1px solid" borderColor="red.200" borderRadius="md">
                <HStack gap={3}>
                    <Box color="red.500" fontSize="lg">
                        <FaExclamationTriangle />
                    </Box>
                    <VStack align="start" gap={1}>
                        <Text fontSize="md" fontWeight="bold" color="red.700">Payment Overdue!</Text>
                        <Text fontSize="sm" color="red.600">
                            The payment deadline has passed. Please contact support immediately to resolve this issue.
                        </Text>
                        <Text fontSize="xs" color="red.500">
                            Deadline was: {format(new Date(auctionWinnerDetails.paymentDeadline), 'PPP p')}
                        </Text>
                    </VStack>
                </HStack>
            </Box>
        )
    }

    if (timeRemaining.isUrgent) {
        return (
            <Box p={4} bg="orange.50" border="1px solid" borderColor="orange.200" borderRadius="md">
                <HStack gap={3}>
                    <Box color="orange.500" fontSize="lg">
                        <FaClock />
                    </Box>
                    <VStack align="start" gap={1}>
                        <Text fontSize="md" fontWeight="bold" color="orange.700">Payment Due Soon!</Text>
                        <Text fontSize="sm" color="orange.600">
                            Only {formatPaymentDeadline(auctionWinnerDetails.paymentDeadline)} left to complete your payment!
                        </Text>
                        <Text fontSize="xs" color="orange.500">
                            Deadline: {format(new Date(auctionWinnerDetails.paymentDeadline), 'PPP p')}
                        </Text>
                    </VStack>
                </HStack>
            </Box>
        )
    }

    return (
        <Box p={4} bg="blue.50" border="1px solid" borderColor="blue.200" borderRadius="md">
            <HStack gap={3}>
                <Box color="blue.500" fontSize="lg">
                    <FaCheckCircle />
                </Box>
                <VStack align="start" gap={1}>
                    <Text fontSize="md" fontWeight="bold" color="blue.700">Payment Deadline</Text>
                    <Text fontSize="sm" color="blue.600">
                        You have {formatPaymentDeadline(auctionWinnerDetails.paymentDeadline)} to complete your payment.
                    </Text>
                    <Text fontSize="xs" color="blue.500">
                        Deadline: {format(new Date(auctionWinnerDetails.paymentDeadline), 'PPP p')}
                    </Text>
                </VStack>
            </HStack>
        </Box>
    )
}

const BuyNowCheckoutPage = () => {
    const { data: dataSession } = useSession()
    const router = useRouter()
    const searchParams = useSearchParams()
    const productId = searchParams.get('productId')
    const checkoutType = searchParams.get('type') || 'buy-now' // 'buy-now' or 'bidding'
    const bidId = searchParams.get('bidId') // For bidding orders

    const [productCart, setProductCart] = useState<CartItem[]>([])
    const [hasError, setHasError] = useState(false)
    const [errorMessage, setErrorMessage] = useState('')

    // Enhanced data fetching with better error handling
    const { data: cart, isLoading: cartLoading, error: cartError } = useCartQuery(
        checkoutType === 'bidding' ? undefined : productId || '',
        checkoutType === 'bidding' ? undefined : dataSession?.user.id || ''
    );

    const { data: product, isLoading: productLoading, error: productError } = useProductByIdQuery(
        productId || ''
    )

    const { data: bidData, isLoading: bidLoading, error: bidError } = useBidDetailQuery(
        checkoutType === 'bidding' ? bidId || '' : ''
    )

    // Enhanced auction payment integration
    const { data: auctionWinnerDetails, isLoading: auctionLoading } = useAuctionWinnerDetailsQuery(
        checkoutType === 'bidding' && bidId ? bidId : ''
    )

    // If no bidId but we have productId for bidding, try to get winner details by productId
    const { data: auctionWinnerByProduct, isLoading: auctionByProductLoading } = useAuctionWinnerByProductQuery(
        checkoutType === 'bidding' && !bidId && productId ? productId : ''
    )

    // Also try to get user bids directly as another fallback
    const { data: userBidsData, isLoading: userBidsLoading } = useUserBidsQuery({
        page: 1,
        limit: 100,
        sortBy: 'createdAt',
        sortOrder: 'desc'
    })

    // Fetch user orders to check if order already exists for this product
    const { data: ordersData } = useUserOrdersQuery({
        page: 1,
        limit: 100,
        sortBy: 'createdAt',
        sortOrder: 'desc'
    })

    const buyNowMutation = useBuyNowMutation()
    const createInvoiceMutation = useCreateInvoiceMutation()
    const createPaymentMutation = useCreatePaymentMutation()

    // Use global currency context
    const { currency: selectedCurrency, convertAndFormatPrice } = useCurrencyLanguage()

    // Determine if this is a bidding checkout
    const isBiddingCheckout = checkoutType === 'bidding'

    // Enhanced loading state
    const isLoading = cartLoading || productLoading || bidLoading || auctionLoading || auctionByProductLoading || userBidsLoading



    // Find winning bid from user bids data as additional fallback
    const winningBidFromUserData = React.useMemo(() => {
        if (!userBidsData?.bids || !productId) return null

        const winningBid = userBidsData.bids.find(bid =>
            bid.productId === productId && bid.auctionStatus === 'won'
        )

        if (winningBid) {
            const now = new Date()
            const auctionEndDate = new Date(winningBid.product.auctionEndDate || now)
            const paymentDeadline = new Date(auctionEndDate.getTime() + (3 * 24 * 60 * 60 * 1000))

            return {
                id: `winner-${productId}`,
                bidId: `bid-${productId}`,
                productId: productId,
                productName: winningBid.product.itemName,
                productSlug: winningBid.product.slug,
                productImage: winningBid.product.images?.find((img: any) => img.isMain)?.imageUrl,
                winningBid: winningBid.highestBid,
                auctionEndDate: winningBid.product.auctionEndDate || now.toISOString(),
                paymentDeadline: paymentDeadline.toISOString(),
                paymentStatus: (winningBid as any).orderStatus === 'paid' ? 'paid' as const : 'pending' as const,
                orderId: (winningBid as any).orderId,
                orderNumber: (winningBid as any).orderNumber,
                orderStatus: (winningBid as any).orderStatus,
                invoiceUrl: (winningBid as any).invoiceUrl,
                paymentUrl: (winningBid as any).paymentUrl,
                isPaymentOverdue: now > paymentDeadline,
                hoursRemaining: Math.max(0, Math.floor((paymentDeadline.getTime() - now.getTime()) / (1000 * 60 * 60)))
            }
        }

        return null
    }, [userBidsData?.bids, productId])

    // Get the appropriate auction winner details (try multiple sources)
    const finalAuctionWinnerDetails = auctionWinnerDetails || auctionWinnerByProduct || winningBidFromUserData

    // Check if order already exists for this auction (either from auction winner details or existing orders)
    const existingOrderForBid = React.useMemo(() => {
        if (!isBiddingCheckout || !productId) return null

        // Check if order already exists in finalAuctionWinnerDetails
        if (finalAuctionWinnerDetails?.orderId) {
            return { source: 'auction_winner', orderId: finalAuctionWinnerDetails.orderId }
        }

        // Check if order already exists in user's orders for this product
        if (ordersData?.orders) {
            const existingOrder = ordersData.orders.find((order: any) =>
                order.items.some((item: any) =>
                    item.productId === productId &&
                    // For bidding checkout, ensure this is actually a bidding order
                    (item.bidId || item.product?.sellType === 'auction')
                )
            )
            if (existingOrder) {
                return { source: 'user_orders', orderId: existingOrder.id }
            }
        }

        return null
    }, [isBiddingCheckout, productId, finalAuctionWinnerDetails?.orderId, ordersData?.orders])

    // Debug logging
    React.useEffect(() => {
        if (isBiddingCheckout) {
            console.log('Checkout Debug Info:');
            console.log('- productId:', productId);
            console.log('- bidId:', bidId);
            console.log('- bidData:', bidData);
            console.log('- auctionWinnerDetails:', auctionWinnerDetails);
            console.log('- auctionWinnerByProduct:', auctionWinnerByProduct);
            console.log('- userBidsData:', userBidsData);
            console.log('- winningBidFromUserData:', winningBidFromUserData);
            console.log('- finalAuctionWinnerDetails:', finalAuctionWinnerDetails);
            console.log('- isLoading:', isLoading);
        }
    }, [isBiddingCheckout, productId, bidId, bidData, auctionWinnerDetails, auctionWinnerByProduct, userBidsData, winningBidFromUserData, finalAuctionWinnerDetails, isLoading])

    // Error handling
    useEffect(() => {
        if (!productId) {
            setHasError(true)
            setErrorMessage('Product ID is required for checkout')
            return
        }

        // For bidding checkout, we need either bidId or we'll find the winning bid from productId
        // This is not an error condition anymore

        if (productError) {
            setHasError(true)
            setErrorMessage('Failed to load product information')
            return
        }

        // Only set error for bidError if we also don't have auctionWinnerByProduct data
        if (bidError && checkoutType === 'bidding' && !finalAuctionWinnerDetails) {
            setHasError(true)
            setErrorMessage('Failed to load bidding information')
            return
        }

        if (cartError && checkoutType !== 'bidding') {
            setHasError(true)
            setErrorMessage('Failed to load cart information')
            return
        }

        // Reset error if everything is okay
        setHasError(false)
        setErrorMessage('')
    }, [productId, bidId, checkoutType, productError, bidError, cartError, finalAuctionWinnerDetails, winningBidFromUserData])

    const countriesOptions = countries.map(country => ({
        value: country.alpha2,
        label: `${country.emoji} ${country.name} (${country.alpha2})`,
    }))

    const {
        register,
        handleSubmit,
        formState: { errors, isSubmitting },
        watch,
        control,
        setValue
    } = useForm<BuyNowFormData>({
        defaultValues: {
            country: '',
            paymentMethod: 'xendit_invoice',
            currency: selectedCurrency
        }
    })

    useEffect(() => {
        // Reset product cart when switching between checkout types
        setProductCart([])

        if (isBiddingCheckout && product) {
            // For bidding checkout, use the winning bid amount from bidData or auctionWinnerDetails
            const winningBid = bidData?.amount || finalAuctionWinnerDetails?.winningBid || product.priceUSD
            const products = [{
                id: product.id,
                productId: product.id,
                quantity: 1,
                price: winningBid, // Use winning bid amount
                product: {
                    id: product.id,
                    itemName: product.itemName,
                    slug: product.slug,
                    priceUSD: winningBid, // Use winning bid amount
                    images: product.images || [],
                    sellType: 'auction' as const, // Mark as auction for bidding
                    status: product.status
                },
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            }]
            setProductCart(products)
        } else if (cart?.items && cart.items.length > 0 && !isBiddingCheckout) {
            // Use cart items for regular checkout
            const buyNowItems = cart.items.filter(item => item.product.sellType === 'buy-now')
            if (buyNowItems.length > 0) {
                setProductCart(buyNowItems)
            }
        } else if (product && !isBiddingCheckout && product.sellType === 'buy-now') {
            // Direct product checkout (buy now)
            const products = [{
                id: product.id,
                productId: product.id,
                quantity: 1,
                price: product.priceUSD,
                product: {
                    id: product.id,
                    itemName: product.itemName,
                    slug: product.slug,
                    priceUSD: product.priceUSD,
                    images: product.images || [],
                    sellType: product.sellType,
                    status: product.status
                },
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            }]
            setProductCart(products)
        }
    }, [cart, product, bidData, isBiddingCheckout, checkoutType, finalAuctionWinnerDetails?.winningBid, winningBidFromUserData])

    const onSubmit = async (data: BuyNowFormData) => {
        if (!productCart || productCart.length === 0) {
            toaster.create({
                title: "Error",
                description: "Product not found",
                type: "error",
            })
            return
        }

        // Validate product type based on checkout type
        if (isBiddingCheckout) {
            if (productCart.filter(item => item.product.sellType === 'auction').length !== productCart.length) {
                toaster.create({
                    title: "Error",
                    description: "This product is not available for bidding checkout",
                    type: "error",
                })
                return
            }
        } else {
            if (productCart.filter(item => item.product.sellType === 'buy-now').length !== productCart.length) {
                toaster.create({
                    title: "Error",
                    description: "This product is not available for direct purchase",
                    type: "error",
                })
                return
            }
        }

        try {
            // Unified order creation for both buy-now and bidding checkout
            const orderData = {
                products: productCart.map(item => item.id),
                currency: data.currency, // Pass the selected currency
                shippingAddress: {
                    name: data.name,
                    address: data.address,
                    city: data.city,
                    provinceRegion: data.provinceRegion,
                    zipCode: data.zipCode,
                    country: data.country,
                },
                paymentMethod: {
                    type: data.paymentMethod,
                    method: data.selectedPaymentMethod,
                    channel: data.paymentChannel,
                    ewalletType: data.ewalletType,
                    bankCode: data.bankCode,
                    retailOutletName: data.retailOutletName,
                },
                notes: data.notes,
                // Add bidding information if this is a bidding checkout
                ...(isBiddingCheckout && {
                    bidId: bidData?.id || finalAuctionWinnerDetails?.bidId || '',
                    orderType: 'bidding',
                    winningBid: bidData?.amount || finalAuctionWinnerDetails?.winningBid || productCart[0].price
                })
            }

            const order = await buyNowMutation.mutateAsync(orderData)

            // Create payment based on selected method type
            const basePaymentData = {
                orderId: order.id,
                currency: data.currency,
                customerEmail: dataSession?.user?.email || '',
                customerName: `${dataSession?.user?.firstName || ''} ${dataSession?.user?.lastName || ''}`.trim(),
                description: `Payment for Order #${order.orderNumber}`,
                successRedirectUrl: isBiddingCheckout
                    ? `${window.location.origin}/account/bidding?payment=success&orderId=${order.id}`
                    : `${window.location.origin}/account/buying/${order.id}?payment=success`,
                failureRedirectUrl: isBiddingCheckout
                    ? `${window.location.origin}/account/bidding?payment=failed&orderId=${order.id}`
                    : `${window.location.origin}/account/buying/${order.id}?payment=failed`,
            }

            let paymentResult;

            if (data.paymentMethod === 'ewallet' && data.ewalletType) {
                // Create eWallet payment
                paymentResult = await createPaymentMutation.mutateAsync({
                    ...basePaymentData,
                    paymentType: 'ewallet',
                    ewalletType: data.ewalletType,
                    customerPhone: dataSession?.user?.phoneNumber || '',
                })

            } else if (data.paymentMethod === 'virtual_account' && data.bankCode) {
                // Create Virtual Account payment
                paymentResult = await createPaymentMutation.mutateAsync({
                    ...basePaymentData,
                    paymentType: 'virtual_account',
                    bankCode: data.bankCode,
                })

            } else if (data.paymentMethod === 'retail_outlet' && data.retailOutletName) {
                // Create Retail Outlet payment
                paymentResult = await createPaymentMutation.mutateAsync({
                    ...basePaymentData,
                    paymentType: 'retail_outlet',
                    retailOutletName: data.retailOutletName,
                })

            } else {
                // Default to invoice for all other payment methods
                paymentResult = await createInvoiceMutation.mutateAsync(basePaymentData)
            }

            toaster.create({
                title: isBiddingCheckout ? "Auction Payment Processed" : "Order Created",
                description: isBiddingCheckout
                    ? `Auction payment for Order #${order.orderNumber} processed. Redirecting to order tracking...`
                    : `Order #${order.orderNumber} created. Redirecting to payment...`,
                type: "success",
            })

            if (paymentResult?.invoiceUrl) {
                openPaymentUrl(paymentResult.invoiceUrl)
            }

            setTimeout(() => {
                if (isBiddingCheckout) {
                    router.push(`/account/bidding?orderId=${order.id}`)
                } else {
                    router.push(`/order-tracking/${order.id}`)
                }
            }, 1000)
        } catch (error) {
            console.error(isBiddingCheckout ? 'Auction payment failed:' : 'Buy now failed:', error)

            toaster.create({
                title: isBiddingCheckout ? "Auction Payment Failed" : "Order Creation Failed",
                description: isBiddingCheckout
                    ? "Failed to process auction payment. Please try again."
                    : "Failed to create order. Please try again.",
                type: "error",
                duration: 5000,
            })
        }
    }

    if (productLoading || isLoading) {
        return (
            <Box maxW="6xl" mx="auto" p={6}>
                <Text>Loading product...</Text>
            </Box>
        )
    }

    if (!productCart.length) {
        return (
            <Box maxW="6xl" mx="auto" p={6} textAlign="center">
                <Alert status="error">
                    <AlertIcon status="error" />
                    <VStack align="start" flex={1}>
                        <AlertTitle>Product not found!</AlertTitle>
                        <AlertDescription>
                            The product you're trying to purchase could not be found.
                        </AlertDescription>
                    </VStack>
                </Alert>
                <Button mt={4} onClick={() => router.push('/')}>
                    Continue Shopping
                </Button>
            </Box>
        )
    }

    // Calculate totals in USD first, then convert
    const subtotalUSD = productCart.reduce((total, item) => {
        return total + Number(item.product.priceUSD ?? 0)
    }, 0)

    // Get shipping cost from selected option
    const selectedShipping = watch('shippingOption')
    const shippingUSD = selectedShipping?.cost || 0

    const taxUSD = subtotalUSD * 0.1
    const totalUSD = subtotalUSD + shippingUSD + taxUSD

    // Convert to selected currency for display using context
    const subtotal = convertAndFormatPrice(subtotalUSD, 'USD')
    const shipping = convertAndFormatPrice(shippingUSD, 'USD')
    const tax = convertAndFormatPrice(taxUSD, 'USD')
    const total = convertAndFormatPrice(totalUSD, 'USD')

    // Show error state
    if (hasError) {
        return (
            <Box maxW="6xl" mx="auto" p={6}>
                <VStack gap={6} align="center" py={12}>
                    <Box color="red.500" fontSize="4xl">
                        <FaExclamationTriangle />
                    </Box>
                    <VStack gap={2} textAlign="center">
                        <Heading size="lg" color="red.600">Checkout Error</Heading>
                        <Text color="gray.600">{errorMessage}</Text>
                    </VStack>
                    <HStack gap={3}>
                        <Button variant="outline" onClick={() => router.back()}>
                            Go Back
                        </Button>
                        <Button colorPalette="blue" onClick={() => router.push('/marketplace')}>
                            Browse Products
                        </Button>
                    </HStack>
                </VStack>
            </Box>
        )
    }

    // Show loading state
    if (isLoading) {
        return (
            <Box maxW="6xl" mx="auto" p={6}>
                <VStack gap={6} align="center" py={12}>
                    <Box color="blue.500" fontSize="4xl">
                        <FaClock />
                    </Box>
                    <VStack gap={2} textAlign="center">
                        <Heading size="lg" color="blue.600">Loading Checkout</Heading>
                        <Text color="gray.600">
                            {isBiddingCheckout ? 'Loading your auction details...' : 'Loading product information...'}
                        </Text>
                    </VStack>
                </VStack>
            </Box>
        )
    }

    // Validate required data for bidding checkout
    if (isBiddingCheckout && !product) {
        return (
            <Box maxW="6xl" mx="auto" p={6}>
                <VStack gap={6} align="center" py={12}>
                    <Box color="orange.500" fontSize="4xl">
                        <FaExclamationTriangle />
                    </Box>
                    <VStack gap={2} textAlign="center">
                        <Heading size="lg" color="orange.600">Product Not Found</Heading>
                        <Text color="gray.600">
                            Unable to find the auction product details.
                        </Text>
                    </VStack>
                    <HStack gap={3}>
                        <Button variant="outline" onClick={() => router.push('/account/bidding')}>
                            View My Bids
                        </Button>
                        <Button colorPalette="blue" onClick={() => router.push('/marketplace')}>
                            Browse Auctions
                        </Button>
                    </HStack>
                </VStack>
            </Box>
        )
    }



    if (isBiddingCheckout && existingOrderForBid) {
        return (
            <Box maxW="6xl" mx="auto" p={6}>
                <VStack gap={6} align="center" py={12}>
                    <Box color="orange.500" fontSize="4xl">
                        <FaExclamationTriangle />
                    </Box>
                    <VStack gap={2} textAlign="center">
                        <Heading size="lg" color="orange.600">Order Already Exists</Heading>
                        <Text color="gray.600">
                            You have already created an order for this auction. Please complete the payment for your existing order.
                        </Text>
                        <Text color="gray.500" fontSize="sm">
                            Multiple orders for the same auction are not allowed.
                        </Text>
                    </VStack>
                    <HStack gap={3}>
                        <Button variant="outline" onClick={() => router.push('/account/bidding')}>
                            View My Bids
                        </Button>
                        {existingOrderForBid?.source === 'auction_winner' && finalAuctionWinnerDetails?.orderId && (
                            <Button colorPalette="blue" onClick={() => router.push(`/order-tracking/${finalAuctionWinnerDetails.orderId}`)}>
                                Complete Payment
                            </Button>
                        )}
                        {existingOrderForBid?.source === 'user_orders' && (
                            <Button colorPalette="blue" onClick={() => router.push(`/order-tracking/${existingOrderForBid.orderId}`)}>
                                Complete Payment
                            </Button>
                        )}
                    </HStack>
                </VStack>
            </Box>
        )
    }

    // Check if user is actually the winner for this auction (only after loading is complete)
    if (isBiddingCheckout && product && !isLoading && !bidData && !finalAuctionWinnerDetails) {
        return (
            <Box maxW="6xl" mx="auto" p={6}>
                <VStack gap={6} align="center" py={12}>
                    <Box color="orange.500" fontSize="4xl">
                        <FaExclamationTriangle />
                    </Box>
                    <VStack gap={2} textAlign="center">
                        <Heading size="lg" color="orange.600">Not Auction Winner</Heading>
                        <Text color="gray.600">
                            You are not the winner of this auction or the auction is still ongoing.
                        </Text>
                        <Text color="gray.500" fontSize="sm">
                            Only auction winners can access the checkout page.
                        </Text>
                        {/* Debug info in development */}
                        {process.env.NODE_ENV === 'development' && (
                            <Box mt={4} p={4} bg="gray.100" borderRadius="md" fontSize="xs" textAlign="left">
                                <Text fontWeight="bold">Debug Info:</Text>
                                <Text>Product ID: {productId}</Text>
                                <Text>Bid ID: {bidId || 'None'}</Text>
                                <Text>Has bidData: {bidData ? 'Yes' : 'No'}</Text>
                                <Text>Has auctionWinnerDetails: {auctionWinnerDetails ? 'Yes' : 'No'}</Text>
                                <Text>Has auctionWinnerByProduct: {auctionWinnerByProduct ? 'Yes' : 'No'}</Text>
                                <Text>Has userBidsData: {userBidsData ? 'Yes' : 'No'}</Text>
                                <Text>Has winningBidFromUserData: {winningBidFromUserData ? 'Yes' : 'No'}</Text>
                                <Text>Has finalAuctionWinnerDetails: {finalAuctionWinnerDetails ? 'Yes' : 'No'}</Text>
                                <Text>Is Loading: {isLoading ? 'Yes' : 'No'}</Text>
                                {userBidsData?.bids && (
                                    <Text>User Bids Count: {userBidsData.bids.length}</Text>
                                )}
                            </Box>
                        )}
                    </VStack>
                    <HStack gap={3}>
                        <Button variant="outline" onClick={() => router.push('/account/bidding')}>
                            View My Bids
                        </Button>
                        <Button colorPalette="blue" onClick={() => router.push(`/auction/product/${productId}`)}>
                            View Auction
                        </Button>
                    </HStack>
                </VStack>
            </Box>
        )
    }

    if (!isBiddingCheckout && !product && !cart?.items?.length) {
        return (
            <Box maxW="6xl" mx="auto" p={6}>
                <VStack gap={6} align="center" py={12}>
                    <Box color="orange.500" fontSize="4xl">
                        <FaExclamationTriangle />
                    </Box>
                    <VStack gap={2} textAlign="center">
                        <Heading size="lg" color="orange.600">Product Not Found</Heading>
                        <Text color="gray.600">
                            Unable to find the product you're trying to purchase.
                        </Text>
                    </VStack>
                    <HStack gap={3}>
                        <Button variant="outline" onClick={() => router.push('/cart')}>
                            View Cart
                        </Button>
                        <Button colorPalette="blue" onClick={() => router.push('/marketplace')}>
                            Browse Products
                        </Button>
                    </HStack>
                </VStack>
            </Box>
        )
    }

    return (
        <Box maxW="6xl" mx="auto" p={6}>
            <Heading mb={6}>
                {isBiddingCheckout ? 'Auction Winner - Checkout' : 'Buy Now - Checkout'}
            </Heading>

            {/* Auction Winner Banner */}
            {isBiddingCheckout && product && (bidData || finalAuctionWinnerDetails) && (
                <VStack gap={4} mb={6}>
                    <AuctionWinnerBanner
                        bidData={bidData || { amount: finalAuctionWinnerDetails?.winningBid || 0 }}
                        product={product}
                    />
                    <PaymentDeadlineWarning auctionWinnerDetails={finalAuctionWinnerDetails} />
                </VStack>
            )}

            <form onSubmit={handleSubmit(onSubmit)}>
                <Grid templateColumns={{ base: "1fr", lg: "2fr 1fr" }} gap={4}>
                    {/* Left Column - Forms */}
                    <GridItem>
                        <VStack align="stretch" gap={4}>
                            {/* Shipping Information */}
                            <Card.Root>
                                <Card.Header>
                                    <Heading size="md">Shipping Information</Heading>
                                </Card.Header>
                                <Card.Body>
                                    <FormControl isInvalid={!!errors.name}>
                                        <FormLabel>Name</FormLabel>
                                        <Input
                                            {...register('name', { required: 'Name is required' })}
                                            placeholder=""
                                        />
                                        <FormErrorMessage>
                                            {errors.name?.message}
                                        </FormErrorMessage>
                                    </FormControl>

                                    <FormControl isInvalid={!!errors.address} mt={4}>
                                        <FormLabel>Address</FormLabel>
                                        <Textarea
                                            {...register('address', { required: 'Address is required' })}
                                            placeholder="JL. Example Street No. 123"
                                            rows={3}
                                        />
                                        <FormErrorMessage>
                                            {errors.address?.message}
                                        </FormErrorMessage>
                                    </FormControl>

                                    <Grid templateColumns={{ base: "1fr", md: "1fr 1fr" }} gap={4} mt={4}>
                                        <Controller
                                            name="country"
                                            control={control}
                                            render={({ field }) => (
                                                <FormSelectField
                                                    label="Country"
                                                    required
                                                    placeholder="Select Country"
                                                    options={countriesOptions}
                                                    width="100%"
                                                    value={countriesOptions.find(opt => opt.value === field.value) || undefined}
                                                    onChange={(selectedOption) => {
                                                        const option = selectedOption as SingleValue<SelectOption>;
                                                        field.onChange(option?.value || '');
                                                    }}
                                                    errorText={errors.country?.message}
                                                />
                                            )}
                                        />
                                        <FormControl isInvalid={!!errors.provinceRegion}>
                                            <FormLabel>Provice / Region</FormLabel>
                                            <Input
                                                {...register('provinceRegion', { required: 'Province / Region is required' })}
                                                placeholder="DKI Jakarta"
                                            />
                                            <FormErrorMessage>
                                                {errors.provinceRegion?.message}
                                            </FormErrorMessage>
                                        </FormControl>
                                        <FormControl isInvalid={!!errors.city}>
                                            <FormLabel>City</FormLabel>
                                            <Input
                                                {...register('city', { required: 'City is required' })}
                                                placeholder="Jakarta"
                                            />
                                            <FormErrorMessage>
                                                {errors.city?.message}
                                            </FormErrorMessage>
                                        </FormControl>

                                        <FormControl isInvalid={!!errors.zipCode}>
                                            <FormLabel>ZIP Code</FormLabel>
                                            <Input
                                                {...register('zipCode', { required: 'ZIP code is required' })}
                                                placeholder="10001"
                                            />
                                            <FormErrorMessage>
                                                {errors.zipCode?.message}
                                            </FormErrorMessage>
                                        </FormControl>
                                    </Grid>

                                </Card.Body>
                            </Card.Root>

                            {/* Shipping Options */}
                            <ShippingOptionsSelector
                                shippingAddress={{
                                    country: watch('country'),
                                    city: watch('city'),
                                    zipCode: watch('zipCode')
                                }}
                                cartItems={productCart as any}
                                onShippingSelect={(option) => {
                                    setValue('shippingOption', option);
                                }}
                                selectedOption={watch('shippingOption')}
                            />

                            {/* Payment Method Selection */}
                            <Controller
                                name="paymentMethod"
                                control={control}
                                rules={{ required: 'Payment method is required' }}
                                render={({ field }) => (
                                    <PaymentMethodSelector
                                        currency={selectedCurrency}
                                        selectedMethod={watch('selectedPaymentMethod')}
                                        selectedType={watch('paymentMethod')}
                                        selectedChannel={watch('paymentChannel')}
                                        onMethodSelect={(method, type, channel) => {
                                            console.log('Payment method selected in checkout:', { method, type, channel })
                                            field.onChange(type);
                                            setValue('selectedPaymentMethod', method);
                                            setValue('paymentChannel', channel || method);

                                            // Set specific fields based on type
                                            if (type === 'ewallet') {
                                                setValue('ewalletType', method);
                                            } else if (type === 'virtual_account') {
                                                setValue('bankCode', method);
                                            } else if (type === 'retail_outlet') {
                                                setValue('retailOutletName', method);
                                            }
                                        }}
                                    />
                                )}
                            />

                            {errors.paymentMethod && (
                                <Text color="red.500" fontSize="sm" mt={2}>
                                    {errors.paymentMethod.message}
                                </Text>
                            )}

                            {/* Order Notes */}
                            <Card.Root>
                                <Card.Header>
                                    <Heading size="md">Order Notes (Optional)</Heading>
                                </Card.Header>
                                <Card.Body>
                                    <FormControl>
                                        <FormLabel>Special Instructions</FormLabel>
                                        <Textarea
                                            {...register('notes')}
                                            placeholder="Any special delivery instructions or notes..."
                                            rows={3}
                                        />
                                    </FormControl>
                                </Card.Body>
                            </Card.Root>
                        </VStack>
                    </GridItem>

                    {/* Right Column - Order Summary */}
                    <GridItem>
                        <Card.Root position="sticky" top={6}>
                            <Card.Header>
                                <Heading size="md">Order Summary</Heading>
                            </Card.Header>
                            <Card.Body>
                                <VStack align="stretch" gap={4}>
                                    {/* Product */}
                                    {
                                        productCart.map(item => (
                                            <HStack key={item.id} gap={3}>
                                                <Image
                                                    src={item.product.images.find(img => img.isMain)?.imageUrl}
                                                    alt={item.product.itemName}
                                                    boxSize="80px"
                                                    objectFit="cover"
                                                    borderRadius="md"
                                                />
                                                <VStack align="start" flex={1} gap={1}>
                                                    <Text fontSize="sm" fontWeight="medium" lineClamp={2}>
                                                        {item.product.itemName}
                                                    </Text>
                                                    <Badge colorScheme={isBiddingCheckout ? "yellow" : "green"}>
                                                        {isBiddingCheckout ? "Auction Winner" : "Buy Now"}
                                                    </Badge>
                                                    <Text fontSize="sm" color="gray.800" fontWeight="bold">
                                                        {convertAndFormatPrice(Number(item.product.priceUSD), 'USD')}
                                                    </Text>
                                                </VStack>
                                            </HStack>
                                        ))
                                    }

                                    {/* Quantity Selector */}
                                    {/* <Box>
                                        <Text fontSize="sm" fontWeight="medium" mb={2}>Quantity</Text>
                                        <HStack>
                                            <IconButton
                                                aria-label="Decrease quantity"
                                                size="sm"
                                                onClick={() => handleQuantityChange(quantity - 1)}
                                                disabled={quantity <= 1}
                                            >
                                                <FaMinus />
                                            </IconButton>
                                            <Text minW="40px" textAlign="center" fontWeight="bold">
                                                {quantity}
                                            </Text>
                                            <IconButton
                                                aria-label="Increase quantity"
                                                size="sm"
                                                onClick={() => handleQuantityChange(quantity + 1)}
                                            >
                                                <FaPlus />
                                            </IconButton>
                                        </HStack>
                                    </Box> */}

                                    <Separator />

                                    {/* Order Totals */}
                                    <VStack align="stretch" gap={2}>
                                        <HStack justify="space-between">
                                            <Text>Subtotal:</Text>
                                            <Text>{subtotal}</Text>
                                        </HStack>
                                        <HStack justify="space-between">
                                            <Text>Shipping:</Text>
                                            <Text>{selectedShipping ? shipping : 'Select shipping option'}</Text>
                                        </HStack>
                                        <HStack justify="space-between">
                                            <Text>Tax:</Text>
                                            <Text>{tax}</Text>
                                        </HStack>
                                        <Separator />
                                        <HStack justify="space-between">
                                            <Text fontWeight="bold" fontSize="lg">Total:</Text>
                                            <Text fontWeight="bold" fontSize="lg" color="gray.800">
                                                {total}
                                            </Text>
                                        </HStack>
                                    </VStack>

                                    <Button
                                        type="submit"
                                        colorScheme="blue"
                                        size="lg"
                                        w="full"
                                        borderRadius="full"
                                        loading={isSubmitting || buyNowMutation.isPending}
                                        disabled={isSubmitting || buyNowMutation.isPending}
                                    >
                                        Complete Purchase
                                    </Button>

                                    <Text fontSize="xs" color="gray.500" textAlign="center">
                                        By placing this order, you agree to our Terms of Service and Privacy Policy.
                                    </Text>
                                </VStack>
                            </Card.Body>
                        </Card.Root>
                    </GridItem>
                </Grid>
            </form>
        </Box>
    )
}

export default BuyNowCheckoutPage
