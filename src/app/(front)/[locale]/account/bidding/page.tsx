'use client'
import React, { useState } from 'react'
import {
    Box,
    Button,
    Heading,
    Text,
    VStack,
    HStack,
    Image,
    Badge,
    Grid,
    Skeleton,
    Flex,
    Container,
    Separator,
    SimpleGrid,
    Icon,
} from '@chakra-ui/react'
import { Tooltip } from '@/components/ui/tooltip'
import {
    FaEye,
    FaGavel,
    FaClock,
    FaDollarSign,
    FaTrophy,
    FaTimesCircle,
    FaCheckCircle,
    FaSpinner,
    FaCreditCard,
    FaShippingFast,
    FaBoxOpen,
    FaExclamationTriangle,
    FaCalendarAlt,
    FaArrowUp,
    FaArrowDown,
    FaReceipt,
    FaInfoCircle,
    FaExternalLinkAlt,
    FaHandshake,
} from 'react-icons/fa'
import { useUserBidsQuery, UserBidsQueryParams } from '@/services/useBiddingQuery'
import { useUserOrdersQuery } from '@/services/useOrderTrackingQuery'
// import { useBiddingStatusSummaryQuery } from '@/services/useBiddingStatusQuery'
// import BiddingStatusDashboard from '@/components/bidding/BiddingStatusDashboard'
import { formatUSD } from '@/utils/helpers/helper'
import { formatDistanceToNow, format, addDays } from 'date-fns'
import { useRouter, useSearchParams } from 'next/navigation'
import FormSelectField, { SelectOption } from '@/components/ui/form/FormSelectField'
import { SingleValue } from 'react-select'

import { useSession } from 'next-auth/react'
import { toaster } from '@/components/ui/toaster'

// Status options
const statusOptions: SelectOption[] = [
    { value: 'all', label: 'All Status' },
    { value: 'active', label: 'Active Auctions' },
    { value: 'ended', label: 'Ended Auctions' },
    { value: 'won', label: 'Won Auctions' },
    { value: 'lost', label: 'Lost Auctions' },
]

// Enhanced Auction status badge component
const AuctionStatusBadge = ({ status }: { status: string }) => {
    const getStatusConfig = (status: string) => {
        switch (status) {
            case 'active':
                return {
                    color: 'blue',
                    icon: FaSpinner,
                    label: 'Active',
                    description: 'Auction is ongoing'
                }
            case 'ended':
                return {
                    color: 'gray',
                    icon: FaClock,
                    label: 'Ended',
                    description: 'Auction has ended'
                }
            case 'won':
                return {
                    color: 'green',
                    icon: FaTrophy,
                    label: 'Won',
                    description: 'You won this auction!'
                }
            case 'lost':
                return {
                    color: 'red',
                    icon: FaTimesCircle,
                    label: 'Lost',
                    description: 'You did not win this auction'
                }
            default:
                return {
                    color: 'gray',
                    icon: FaClock,
                    label: 'Unknown',
                    description: 'Status unknown'
                }
        }
    }

    const config = getStatusConfig(status)

    return (
        <Tooltip content={config.description}>
            <Badge
                colorPalette={config.color}
                variant="subtle"
                px={3}
                py={1}
                borderRadius="full"
                fontSize="xs"
                fontWeight="semibold"
            >
                <HStack gap={1.5}>
                    <Icon>
                        <config.icon />
                    </Icon>
                    <Text>{config.label}</Text>
                </HStack>
            </Badge>
        </Tooltip>
    )
}

// Enhanced Winning status badge component
const WinningStatusBadge = ({ isWinning, auctionStatus }: { isWinning: boolean; auctionStatus: string }) => {
    if (auctionStatus === 'active') {
        return (
            <Tooltip content={isWinning ? 'You have the highest bid' : 'Someone has outbid you'}>
                <Badge
                    colorPalette={isWinning ? 'green' : 'orange'}
                    variant="outline"
                    size="sm"
                    px={2}
                    py={1}
                    borderRadius="full"
                    fontSize="xs"
                    fontWeight="semibold"
                >
                    <HStack gap={1}>
                        <Icon>
                            {isWinning ? <FaArrowUp /> : <FaArrowDown />}
                        </Icon>
                        <Text>{isWinning ? 'Leading' : 'Outbid'}</Text>
                    </HStack>
                </Badge>
            </Tooltip>
        )
    }
    return null
}

// Order Status Badge for won auctions
const OrderStatusBadge = ({ orderStatus }: { orderStatus?: string }) => {
    if (!orderStatus) return null

    const getOrderStatusConfig = (status: string) => {
        switch (status) {
            case 'pending_payment':
                return { color: 'orange', icon: FaCreditCard, label: 'Payment Pending' }
            case 'paid':
                return { color: 'blue', icon: FaCheckCircle, label: 'Payment Confirmed' }
            case 'processing':
                return { color: 'purple', icon: FaBoxOpen, label: 'Processing' }
            case 'shipped':
                return { color: 'cyan', icon: FaShippingFast, label: 'Shipped' }
            case 'delivered':
                return { color: 'green', icon: FaCheckCircle, label: 'Delivered' }
            case 'cancelled':
                return { color: 'red', icon: FaTimesCircle, label: 'Cancelled' }
            default:
                return { color: 'gray', icon: FaInfoCircle, label: status }
        }
    }

    const config = getOrderStatusConfig(orderStatus)

    return (
        <Badge
            colorPalette={config.color}
            variant="solid"
            px={2}
            py={1}
            borderRadius="full"
            fontSize="xs"
            fontWeight="semibold"
        >
            <HStack gap={1}>
                <Icon>
                    <config.icon />
                </Icon>
                <Text>{config.label}</Text>
            </HStack>
        </Badge>
    )
}

// Payment urgency indicator
const PaymentUrgencyIndicator = ({ auctionEndDate, orderStatus }: { auctionEndDate?: string; orderStatus?: string }) => {
    if (!auctionEndDate || orderStatus !== 'pending_payment') return null

    const endDate = new Date(auctionEndDate)
    const paymentDeadline = addDays(endDate, 3) // 3 days to pay
    const now = new Date()
    const hoursLeft = Math.max(0, Math.floor((paymentDeadline.getTime() - now.getTime()) / (1000 * 60 * 60)))

    if (hoursLeft <= 0) {
        return (
            <Box
                bg="red.50"
                border="1px solid"
                borderColor="red.200"
                borderRadius="md"
                p={3}
                fontSize="sm"
            >
                <HStack gap={2}>
                    <Icon color="red.500">
                        <FaExclamationTriangle />
                    </Icon>
                    <VStack align="start" gap={0}>
                        <Text fontWeight="semibold" color="red.700">Payment Overdue!</Text>
                        <Text fontSize="xs" color="red.600">
                            Payment deadline has passed. Contact support.
                        </Text>
                    </VStack>
                </HStack>
            </Box>
        )
    }

    if (hoursLeft <= 24) {
        return (
            <Box
                bg="orange.50"
                border="1px solid"
                borderColor="orange.200"
                borderRadius="md"
                p={3}
                fontSize="sm"
            >
                <HStack gap={2}>
                    <Icon color="orange.500">
                        <FaClock />
                    </Icon>
                    <VStack align="start" gap={0}>
                        <Text fontWeight="semibold" color="orange.700">Payment Due Soon!</Text>
                        <Text fontSize="xs" color="orange.600">
                            Pay within {hoursLeft} hours to secure your item.
                        </Text>
                    </VStack>
                </HStack>
            </Box>
        )
    }

    return null
}

// Order Progress Indicator
const OrderProgressIndicator = ({ order }: { order: any }) => {
    if (!order) return null

    const getProgressSteps = () => {
        const steps = [
            { key: 'pending_payment', label: 'Payment Pending', icon: FaCreditCard },
            { key: 'paid', label: 'Payment Confirmed', icon: FaCheckCircle },
            { key: 'processing', label: 'Processing', icon: FaBoxOpen },
            { key: 'shipped', label: 'Shipped', icon: FaShippingFast },
            { key: 'delivered', label: 'Delivered', icon: FaTrophy },
        ]

        const currentStepIndex = steps.findIndex(step => step.key === order.status)

        return steps.map((step, index) => ({
            ...step,
            isCompleted: index < currentStepIndex,
            isCurrent: index === currentStepIndex,
            isUpcoming: index > currentStepIndex
        }))
    }

    const steps = getProgressSteps()
    const progressPercentage = ((steps.findIndex(s => s.isCurrent) + 1) / steps.length) * 100

    return (
        <Box p={4} bg="gray.50" borderRadius="lg" border="1px solid" borderColor="gray.200">
            <VStack gap={4} align="stretch">
                <HStack justify="space-between" align="center">
                    <Text fontSize="sm" fontWeight="semibold" color="gray.700">
                        Order Progress
                    </Text>
                    <Text fontSize="xs" color="gray.600">
                        Order #{order.orderNumber}
                    </Text>
                </HStack>

                {/* Progress Bar */}
                <Box position="relative">
                    <Box h="2px" bg="gray.200" borderRadius="full" />
                    <Box
                        h="2px"
                        bg="blue.500"
                        borderRadius="full"
                        width={`${progressPercentage}%`}
                        transition="width 0.3s ease"
                    />
                </Box>

                {/* Progress Steps */}
                <HStack justify="space-between" align="start">
                    {steps.map((step, index) => (
                        <VStack key={step.key} gap={1} align="center" flex={1}>
                            <Box
                                w="8"
                                h="8"
                                borderRadius="full"
                                bg={step.isCompleted ? 'green.500' : step.isCurrent ? 'blue.500' : 'gray.300'}
                                color="white"
                                display="flex"
                                alignItems="center"
                                justifyContent="center"
                                fontSize="xs"
                            >
                                <Icon>
                                    <step.icon />
                                </Icon>
                            </Box>
                            <Text
                                fontSize="xs"
                                textAlign="center"
                                color={step.isCompleted ? 'green.600' : step.isCurrent ? 'blue.600' : 'gray.500'}
                                fontWeight={step.isCurrent ? 'semibold' : 'normal'}
                            >
                                {step.label}
                            </Text>
                        </VStack>
                    ))}
                </HStack>

                {/* Estimated Delivery */}
                {order.estimatedDelivery && (
                    <HStack gap={2} justify="center" p={2} bg="blue.50" borderRadius="md">
                        <Icon color="blue.500">
                            <FaCalendarAlt />
                        </Icon>
                        <Text fontSize="sm" color="blue.700">
                            Estimated delivery: {format(new Date(order.estimatedDelivery), 'PPP')}
                        </Text>
                    </HStack>
                )}
            </VStack>
        </Box>
    )
}

const AccountBiddingPage = () => {
    const router = useRouter()
    const searchParams = useSearchParams()
    const { data: session } = useSession()
    const [statusFilter, setStatusFilter] = useState<string>('all')
    const [currentPage, setCurrentPage] = useState(1)

    // Handle payment success/failed notifications
    React.useEffect(() => {
        const payment = searchParams.get('payment')
        const orderId = searchParams.get('orderId')

        if (payment === 'success' && orderId) {
            toaster.create({
                title: "Payment Successful! 🎉",
                description: `Your auction payment has been processed successfully. Order ID: ${orderId}`,
                type: "success",
                duration: 5000,
            })

            // Clean up URL parameters
            const newUrl = window.location.pathname
            window.history.replaceState({}, '', newUrl)
        } else if (payment === 'failed' && orderId) {
            toaster.create({
                title: "Payment Failed",
                description: `Payment for your auction order failed. Please try again. Order ID: ${orderId}`,
                type: "error",
                duration: 5000,
            })

            // Clean up URL parameters
            const newUrl = window.location.pathname
            window.history.replaceState({}, '', newUrl)
        }
    }, [searchParams])

    // Query parameters for user's bids
    const queryParams: UserBidsQueryParams = {
        page: currentPage,
        limit: 10,
        status: statusFilter !== 'all' ? statusFilter as any : undefined,
        sortBy: 'createdAt',
        sortOrder: 'desc',
    }

    const { data: bidsData, isLoading, error } = useUserBidsQuery(queryParams)

    // Fetch orders for won auctions to get order status
    const { data: ordersData } = useUserOrdersQuery({
        page: 1,
        limit: 100, // Get all orders to match with won bids
        sortBy: 'createdAt',
        sortOrder: 'desc'
    })

    // Fetch enhanced status data
    // const { data: statusSummary, isLoading: isLoadingSummary } = useBiddingStatusSummaryQuery()
    // const { data: statusItems, isLoading: isLoadingStatus } = useBiddingItemsStatusQuery({
    //     page: currentPage,
    //     limit: 10,
    //     status: statusFilter !== 'all' ? statusFilter : undefined,
    //     sortBy: 'createdAt',
    //     sortOrder: 'desc'
    // })

    const handleStatusChange = (option: SingleValue<SelectOption>) => {
        setStatusFilter(option?.value || 'all')
        setCurrentPage(1)
    }

    const handleViewBid = (productId: string, productSlug?: string) => {
        if (productSlug) {
            router.push(`/auction/${productSlug}`)
        } else {
            router.push(`/auction/product/${productId}`)
        }
    }

    // Function to find order for a won bid
    const findOrderForBid = React.useCallback((productId: string, bidId?: string) => {
        if (!ordersData?.orders) return null

        return ordersData.orders.find(order =>
            order.items.some(item =>
                item.productId === productId &&
                (bidId ? item.bidId === bidId : true)
            )
        )
    }, [ordersData?.orders])

    // Handler functions for bidding actions
    const handlePayNow = React.useCallback((productId: string) => {
        // Find the order for this product
        const order = findOrderForBid(productId)

        if (order?.payment?.invoiceUrl) {
            // If invoice URL exists, open it directly (Xendit payment)
            window.open(order.payment.invoiceUrl, '_blank')

            toaster.create({
                title: "Opening Payment",
                description: "Redirecting to Xendit payment page...",
                type: "info",
                duration: 3000,
            })
        } else if (order && !order.payment?.invoiceUrl) {
            // Order exists but no payment created yet - redirect to order detail to create payment
            router.push(`/order-tracking/${order.id}`)

            toaster.create({
                title: "Complete Payment",
                description: "Taking you to order details to complete payment...",
                type: "info",
                duration: 2000,
            })
        } else {
            // No order exists yet - redirect to checkout to create order
            router.push(`/checkout?type=bidding&productId=${productId}`)

            toaster.create({
                title: "Create Order",
                description: "Taking you to checkout to create your order...",
                type: "info",
                duration: 2000,
            })
        }
    }, [router, findOrderForBid])

    const handleViewOrder = React.useCallback((orderId: string) => {
        router.push(`/order-tracking/${orderId}`)
    }, [router])

    const handleViewAuction = React.useCallback((productId: string, slug?: string) => {
        if (slug) {
            router.push(`/auction/product/${slug}`)
        } else {
            router.push(`/auction/product/${productId}`)
        }
    }, [router])

    const handleContactSupport = React.useCallback(() => {
        // You can implement contact support logic here
        toaster.create({
            title: "Contact Support",
            description: "Please contact our support team for assistance.",
            type: "info",
            duration: 5000,
        })
    }, [])

    if (!session) {
        return (
            <Container maxW="6xl" py={8}>
                <Box p={8} textAlign="center" bg="white" borderRadius="lg" >
                    <VStack gap={4}>
                        <Icon color="gray.400" fontSize="4xl">
                            <FaGavel />
                        </Icon>
                        <Heading size="lg" color="gray.600">Authentication Required</Heading>
                        <Text color="gray.500">Please login to view your bidding history.</Text>
                        <Button colorPalette="blue" onClick={() => router.push('/auth/signin')}>
                            Sign In
                        </Button>
                    </VStack>
                </Box>
            </Container>
        )
    }

    return (
        <Box bg="gray.50" minH="100vh">
            <Container maxW="6xl" py={8}>
                {/* Enhanced Header with Statistics */}
                <VStack align="start" gap={8} mb={8}>
                    {/* Title Section */}
                    <VStack align="start" gap={2}>
                        <HStack gap={3}>
                            <Icon color="blue.500" fontSize="2xl">
                                <FaGavel />
                            </Icon>
                            <Heading size="2xl" color="gray.800">My Bidding Dashboard</Heading>
                        </HStack>
                        <Text color="gray.600" fontSize="lg">
                            Track your auction bids, wins, and order status
                        </Text>
                    </VStack>

                    {/* Enhanced Status Dashboard */}
                    {/* {statusSummary && (
                        <BiddingStatusDashboard
                            summary={statusSummary}
                            isLoading={isLoadingSummary}
                        />
                    )} */}

                    {/* Filters */}
                    <Box p={6} bg="white"  w="full" borderRadius="lg">
                        <VStack align="start" gap={4}>
                            <Heading size="md" color="gray.700">Filter & Search</Heading>
                            <Grid templateColumns={{ base: '1fr', md: '1fr 2fr' }} gap={4} w="full">
                                <FormSelectField
                                    label="Auction Status"
                                    options={statusOptions}
                                    value={statusOptions.find(opt => opt.value === statusFilter)}
                                    onChange={(selectedOption) => {
                                        const option = selectedOption as SingleValue<SelectOption>;
                                        handleStatusChange(option)
                                    }}
                                    placeholder="Select status..."
                                />
                            </Grid>
                        </VStack>
                    </Box>
                </VStack>

                {/* Enhanced Bidding List */}
                {isLoading ? (
                    <VStack gap={4} align="stretch">
                        {Array.from({ length: 5 }).map((_, index) => (
                            <Skeleton key={index} height="300px" borderRadius="lg" />
                        ))}
                    </VStack>
                ) : error ? (
                    <Box p={12} bg="white" borderRadius="xl" textAlign="center">
                        <VStack gap={6}>
                            <Icon color="red.500" fontSize="5xl">
                                <FaTimesCircle />
                            </Icon>
                            <VStack gap={2}>
                                <Heading size="lg" color="red.600">Failed to Load</Heading>
                                <Text color="gray.600">Unable to fetch your bidding history</Text>
                            </VStack>
                            <Button colorPalette="red" variant="outline" onClick={() => window.location.reload()}>
                                Try Again
                            </Button>
                        </VStack>
                    </Box>
                ) : !bidsData?.bids?.length ? (
                    <Box p={12} bg="white" borderRadius="xl" textAlign="center">
                        <VStack gap={6}>
                            <Icon color="gray.400" fontSize="5xl">
                                <FaGavel />
                            </Icon>
                            <VStack gap={2}>
                                <Heading size="lg" color="gray.600">No Bidding History</Heading>
                                <Text color="gray.500">You haven't placed any bids yet</Text>
                            </VStack>
                            <Button colorPalette="blue" onClick={() => router.push('/marketplace?sellType=auction')}>
                                <HStack gap={2}>
                                    <Icon>
                                        <FaExternalLinkAlt />
                                    </Icon>
                                    <Text>Browse Auctions</Text>
                                </HStack>
                            </Button>
                        </VStack>
                    </Box>
                ) : (
                    <VStack gap={6} align="stretch">
                        {bidsData?.bids?.map((bid) => (
                            <Box
                                key={bid.productId}
                                p={8}
                                bg="white"
                                borderRadius="xl"
                                border="1px solid"
                                borderColor="gray.100"
                                transition="all 0.2s"
                                _hover={{ shadow: "lg", transform: "translateY(-2px)" }}
                            >
                                <VStack gap={6} align="stretch">
                                    {/* Enhanced Bid Header */}
                                    <Flex justify="space-between" align="start" wrap="wrap" gap={4}>
                                        <VStack align="start" gap={2}>
                                            <Heading size="lg" color="gray.800">
                                                {bid.product.itemName}
                                            </Heading>
                                            <HStack gap={2}>
                                                <Icon color="gray.500">
                                                    <FaClock />
                                                </Icon>
                                                <Text color="gray.600" fontSize="sm">
                                                    Last bid {formatDistanceToNow(new Date(bid.lastBidTime), { addSuffix: true })}
                                                </Text>
                                            </HStack>
                                        </VStack>
                                        <VStack align="end" gap={2}>
                                            <HStack gap={2}>
                                                <AuctionStatusBadge status={bid.auctionStatus} />
                                                <WinningStatusBadge isWinning={bid.isWinning} auctionStatus={bid.auctionStatus} />
                                            </HStack>
                                            {/* Add order status if won */}
                                            {bid.auctionStatus === 'won' && (() => {
                                                const order = findOrderForBid(bid.productId)
                                                return order ? (
                                                    <OrderStatusBadge orderStatus={order.status} />
                                                ) : (
                                                    <OrderStatusBadge orderStatus="pending_payment" />
                                                )
                                            })()}
                                        </VStack>
                                    </Flex>

                                    {/* Payment urgency and order progress for won auctions */}
                                    {bid.auctionStatus === 'won' && (() => {
                                        const order = findOrderForBid(bid.productId)
                                        const orderStatus = order?.status || 'pending_payment'

                                        return (
                                            <VStack gap={4} align="stretch">
                                                <PaymentUrgencyIndicator
                                                    auctionEndDate={bid.product.auctionEndDate}
                                                    orderStatus={orderStatus}
                                                />
                                                {order && <OrderProgressIndicator order={order} />}
                                            </VStack>
                                        )
                                    })()}

                                    <Separator />

                                    {/* Enhanced Bid Content */}
                                    <Grid templateColumns={{ base: '1fr', md: '200px 1fr' }} gap={8}>
                                        <Box position="relative">
                                            <Image
                                                src={bid.product.images.find((img: any) => img.isMain)?.imageUrl || '/placeholder.jpg'}
                                                alt={bid.product.itemName}
                                                w="full"
                                                h="200px"
                                                objectFit="cover"
                                                borderRadius="lg"
                                                
                                            />
                                            {bid.auctionStatus === 'won' && (
                                                <Box
                                                    position="absolute"
                                                    top={2}
                                                    right={2}
                                                    bg="green.500"
                                                    color="white"
                                                    px={2}
                                                    py={1}
                                                    borderRadius="full"
                                                    fontSize="xs"
                                                    fontWeight="bold"
                                                >
                                                    <HStack gap={1}>
                                                        <Icon>
                                                            <FaTrophy />
                                                        </Icon>
                                                        <Text>WON</Text>
                                                    </HStack>
                                                </Box>
                                            )}
                                        </Box>

                                        <VStack align="start" flex={1} gap={6}>
                                            {/* Bid Information Grid */}
                                            <SimpleGrid columns={{ base: 1, md: 3 }} gap={6} w="full">
                                                <Box>
                                                    <Text fontSize="sm" color="gray.600" mb={1}>Your Highest Bid</Text>
                                                    <Text fontSize="2xl" fontWeight="bold" color="blue.600">
                                                        {formatUSD(bid.highestBid)}
                                                    </Text>
                                                </Box>
                                                <Box>
                                                    <Text fontSize="sm" color="gray.600" mb={1}>Current Leading Bid</Text>
                                                    <Text fontSize="2xl" fontWeight="bold" color="gray.800">
                                                        {formatUSD(bid.product.currentBid || 0)}
                                                    </Text>
                                                </Box>
                                                <Box>
                                                    <Text fontSize="sm" color="gray.600" mb={1}>Total Participants</Text>
                                                    <Text fontSize="2xl" fontWeight="bold" color="purple.600">
                                                        {bid.totalBids}
                                                    </Text>
                                                </Box>
                                            </SimpleGrid>

                                            {/* Auction Timeline */}
                                            {bid.product.auctionEndDate && (
                                                <Box w="full">
                                                    <Text fontSize="sm" color="gray.600" mb={2}>
                                                        Auction Timeline
                                                    </Text>
                                                    <HStack gap={4} wrap="wrap">
                                                        <HStack gap={2}>
                                                            <Icon color="gray.500">
                                                                <FaCalendarAlt />
                                                            </Icon>
                                                            <Text fontSize="sm" color="gray.700">
                                                                {bid.auctionStatus === 'active' ? 'Ends' : 'Ended'}: {' '}
                                                                {format(new Date(bid.product.auctionEndDate), 'PPP p')}
                                                            </Text>
                                                        </HStack>
                                                    </HStack>
                                                </Box>
                                            )}
                                        </VStack>
                                    </Grid>

                                    <Separator />

                                    {/* Enhanced Actions Section */}
                                    <Flex justify="space-between" align="center" wrap="wrap" gap={4}>
                                        <VStack align="start" gap={2}>
                                            <Text fontSize="md" fontWeight="semibold" color="gray.700">
                                                {bid.auctionStatus === 'won' && '🎉 Congratulations! You won this auction'}
                                                {bid.auctionStatus === 'lost' && '😔 Auction ended - You did not win'}
                                                {bid.auctionStatus === 'active' && bid.isWinning && '🔥 You are currently winning!'}
                                                {bid.auctionStatus === 'active' && !bid.isWinning && '⚡ You have been outbid'}
                                                {bid.auctionStatus === 'ended' && '⏰ Auction has ended'}
                                            </Text>
                                            {bid.auctionStatus === 'won' && (
                                                <Text fontSize="sm" color="green.600">
                                                    Complete your purchase to secure this item
                                                </Text>
                                            )}
                                        </VStack>

                                        <HStack gap={3}>
                                            {/* Dynamic buttons based on auction status and order status */}
                                            {bid.auctionStatus === 'won' && (() => {
                                                const order = findOrderForBid(bid.productId)
                                                const paymentStatus = order?.paymentStatus

                                                return (
                                                    <>
                                                        {/* Pay Now Button - only show if order exists and payment is pending */}
                                                        {order && paymentStatus === 'pending' && (
                                                            <Button
                                                                colorPalette="green"
                                                                size="lg"
                                                                onClick={() => handlePayNow(bid.productId)}
                                                            >
                                                                <HStack gap={2}>
                                                                    <Icon>
                                                                        <FaCreditCard />
                                                                    </Icon>
                                                                    <Text>Pay Now</Text>
                                                                </HStack>
                                                            </Button>
                                                        )}

                                                        {/* Complete Payment Button - show if order exists but no payment created yet */}
                                                        {order && !order.payment && (
                                                            <Button
                                                                colorPalette="orange"
                                                                size="lg"
                                                                onClick={() => handlePayNow(bid.productId)}
                                                            >
                                                                <HStack gap={2}>
                                                                    <Icon>
                                                                        <FaCreditCard />
                                                                    </Icon>
                                                                    <Text>Complete Payment</Text>
                                                                </HStack>
                                                            </Button>
                                                        )}

                                                        {/* View Order Button - show if order exists and payment is completed or processing */}
                                                        {order && paymentStatus !== 'pending' && (
                                                            <Button
                                                                colorPalette="blue"
                                                                variant="outline"
                                                                size="lg"
                                                                onClick={() => handleViewOrder(order.id)}
                                                            >
                                                                <HStack gap={2}>
                                                                    <Icon>
                                                                        <FaReceipt />
                                                                    </Icon>
                                                                    <Text>View Detail Order</Text>
                                                                </HStack>
                                                            </Button>
                                                        )}

                                                        {/* Create Order Button - show if no order exists yet */}
                                                        {!order && (
                                                            <Button
                                                                colorPalette="orange"
                                                                size="lg"
                                                                onClick={() => handlePayNow(bid.productId)}
                                                            >
                                                                <HStack gap={2}>
                                                                    <Icon>
                                                                        <FaHandshake />
                                                                    </Icon>
                                                                    <Text>Complete Purchase</Text>
                                                                </HStack>
                                                            </Button>
                                                        )}
                                                    </>
                                                )
                                            })()}

                                            {/* View Auction Button - always show */}
                                            <Button
                                                colorPalette="blue"
                                                variant="outline"
                                                onClick={() => handleViewBid(bid.productId, bid.product.slug)}
                                            >
                                                <HStack gap={2}>
                                                    <Icon>
                                                        <FaEye />
                                                    </Icon>
                                                    <Text>View Auction</Text>
                                                </HStack>
                                            </Button>
                                        </HStack>
                                    </Flex>
                                </VStack>
                            </Box>
                        ))}

                        {/* Enhanced Pagination */}
                        {bidsData?.pagination && bidsData.pagination.totalPages > 1 && (
                            <Box p={6} bg="white" borderRadius="xl" >
                                <Flex justify="center" align="center" gap={4}>
                                    <Button
                                        variant="outline"
                                        colorPalette="blue"
                                        disabled={currentPage === 1}
                                        onClick={() => setCurrentPage(currentPage - 1)}
                                    >
                                        <HStack gap={2}>
                                            <Icon>
                                                <FaArrowDown style={{ transform: 'rotate(90deg)' }} />
                                            </Icon>
                                            <Text>Previous</Text>
                                        </HStack>
                                    </Button>

                                    <VStack gap={1}>
                                        <Text fontSize="sm" color="gray.600">Page</Text>
                                        <Text fontWeight="bold" color="gray.800">
                                            {currentPage} of {bidsData.pagination.totalPages}
                                        </Text>
                                    </VStack>

                                    <Button
                                        variant="outline"
                                        colorPalette="blue"
                                        disabled={currentPage === bidsData.pagination.totalPages}
                                        onClick={() => setCurrentPage(currentPage + 1)}
                                    >
                                        <HStack gap={2}>
                                            <Text>Next</Text>
                                            <Icon>
                                                <FaArrowDown style={{ transform: 'rotate(-90deg)' }} />
                                            </Icon>
                                        </HStack>
                                    </Button>
                                </Flex>
                            </Box>
                        )}
                    </VStack>
                )}
            </Container>
        </Box>
    )
}

export default AccountBiddingPage
