generator client {
  provider      = "prisma-client-js"
  output        = "../generated/client"
  binaryTargets = ["native", "rhel-openssl-3.0.x"]
  engineType    = "binary"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model User {
  id                String            @id @default(uuid())
  userId            String?           @unique
  firstName         String?
  lastName          String?
  email             String?           @unique
  emailVerified     DateTime?
  phoneNumber       String?
  phoneVerified     DateTime?
  role              String            @default("user")
  oauthProvider     String?
  oauthId           String?
  password          String?
  image             String?
  isActive          Boolean           @default(true)
  isEmailVerified   Boolean           @default(false)
  isPhoneVerified   Boolean           @default(false)
  isBlocked         Boolean           @default(false)
  country           String?
  ipAddress         String?
  lastLogin         DateTime?
  lastLoginIp       String?
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt
  googleId          String?           @unique
  refreshToken      String?           @db.Text
  bids              Bid[]
  autoBids          AutoBid[]
  cart              Cart?
  orders            Order[]
  products          Product[]
  shippingAddresses ShippingAddress[]
}

model Category {
  id          String     @id @default(uuid())
  name        String     @unique
  description String?
  isActive    Boolean    @default(true)
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  sellType    String
  itemTypes   ItemType[]
  products    Product[]
}

model ItemType {
  id          String    @id @default(uuid())
  name        String    @unique
  description String?
  categoryId  String?
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  category    Category? @relation(fields: [categoryId], references: [id])
  products    Product[]

  @@index([categoryId])
}

model Product {
  id                      String         @id @default(uuid()) @db.VarChar(300)
  itemName                String         @db.LongText
  description             String?        @db.Text
  sellType                String
  priceUSD                Decimal        @db.Decimal(10, 2)
  auctionStartDate        DateTime?
  auctionEndDate          DateTime?
  currentBid              Decimal?       @db.Decimal(10, 2)
  bidCount                Int            @default(0)
  extendedBiddingEnabled  Boolean        @default(false)
  extendedBiddingMinutes  Int?
  extendedBiddingDuration Int?
  auctionCompleted        Boolean        @default(false)
  auctionCompletedAt      DateTime?
  status                  String         @default("draft")
  isActive                Boolean        @default(true)
  createdAt               DateTime       @default(now())
  updatedAt               DateTime       @updatedAt
  sellerId                String
  categoryId              String
  itemTypeId              String
  slug                    String?        @unique @db.VarChar(300)
  bids                    Bid[]
  autoBids                AutoBid[]
  cartItems               CartItem[]
  orderItems              OrderItem[]
  category                Category       @relation(fields: [categoryId], references: [id])
  itemType                ItemType       @relation(fields: [itemTypeId], references: [id])
  seller                  User           @relation(fields: [sellerId], references: [id])
  images                  ProductImage[]

  @@index([sellerId])
  @@index([categoryId])
  @@index([itemTypeId])
  @@index([status])
  @@index([sellType])
  @@index([slug])
  @@index([auctionCompleted])
  @@index([auctionEndDate])
}

model ProductImage {
  id        String   @id @default(uuid())
  productId String
  imageUrl  String
  altText   String?
  sortOrder Int      @default(0)
  isMain    Boolean  @default(false)
  createdAt DateTime @default(now())
  product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@index([productId])
  @@index([sortOrder])
}

model Bid {
  id               String      @id @default(uuid())
  productId        String
  bidderId         String
  amount           Decimal     @db.Decimal(15, 2)
  isWinning        Boolean     @default(false)
  bidType          String      @default("manual") // "manual" or "auto"
  auctionWon       Boolean     @default(false)
  winnerNotified   Boolean     @default(false)
  winnerNotifiedAt DateTime?
  createdAt        DateTime    @default(now())
  bidder           User        @relation(fields: [bidderId], references: [id])
  product          Product     @relation(fields: [productId], references: [id])
  orderItems       OrderItem[] // Orders created from this bid

  @@index([productId])
  @@index([bidderId])
  @@index([createdAt])
  @@index([isWinning])
  @@index([auctionWon])
}

model Cart {
  id        String     @id @default(uuid())
  userId    String     @unique
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt
  user      User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  items     CartItem[]

  @@index([userId])
}

model CartItem {
  id        String   @id @default(uuid())
  cartId    String
  productId String
  quantity  Int      @default(1)
  price     Decimal  @db.Decimal(10, 2)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  cart      Cart     @relation(fields: [cartId], references: [id], onDelete: Cascade)
  product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([cartId, productId])
  @@index([cartId])
  @@index([productId])
}

model ShippingAddress {
  id             String   @id @default(uuid())
  userId         String
  name           String
  address        String
  city           String
  provinceRegion String
  zipCode        String
  country        String
  isDefault      Boolean  @default(false)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  orders         Order[]
  user           User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}

model Order {
  id                String               @id @default(uuid())
  userId            String
  orderNumber       String               @unique
  status            String               @default("pending")
  paymentStatus     String               @default("pending")
  paymentMethod     String?
  currency          String               @default("USD") // USD, IDR
  subtotal          Decimal              @db.Decimal(10, 2)
  shippingCost      Decimal              @default(0.00) @db.Decimal(10, 2)
  tax               Decimal              @default(0.00) @db.Decimal(10, 2)
  total             Decimal              @db.Decimal(10, 2)
  shippingAddressId String?
  notes             String?              @db.Text
  createdAt         DateTime             @default(now())
  updatedAt         DateTime             @updatedAt
  shippingAddress   ShippingAddress?     @relation(fields: [shippingAddressId], references: [id])
  user              User                 @relation(fields: [userId], references: [id])
  items             OrderItem[]
  statusHistory     OrderStatusHistory[]
  payment           Payment?

  @@index([userId])
  @@index([orderNumber])
  @@index([status])
  @@index([paymentStatus])
  @@index([shippingAddressId], map: "Order_shippingAddressId_fkey")
}

model OrderItem {
  id        String   @id @default(uuid())
  orderId   String
  productId String
  bidId     String? // Link to bid for auction orders
  quantity  Int
  price     Decimal  @db.Decimal(10, 2)
  currency  String   @default("USD") // USD, IDR
  createdAt DateTime @default(now())
  order     Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product   Product  @relation(fields: [productId], references: [id])
  bid       Bid?     @relation(fields: [bidId], references: [id])

  @@index([orderId])
  @@index([productId])
  @@index([bidId])
}

model AutoBid {
  id           String   @id @default(uuid())
  productId    String
  bidderId     String
  startingBid  Decimal  @db.Decimal(10, 2)
  maxBudget    Decimal  @db.Decimal(10, 2)
  bidIncrement Decimal  @db.Decimal(10, 2)
  isActive     Boolean  @default(true)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  bidder       User     @relation(fields: [bidderId], references: [id])
  product      Product  @relation(fields: [productId], references: [id])

  @@unique([productId, bidderId])
  @@index([productId])
  @@index([bidderId])
  @@index([isActive])
}

model OrderStatusHistory {
  id          String   @id @default(uuid())
  orderId     String
  status      String // pending_payment, paid, seller_confirmed, shipped, delivered, cancelled
  description String?
  createdBy   String? // userId who made the status change
  order       Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)
  createdAt   DateTime @default(now())

  @@index([orderId])
  @@index([status])
}

model CurrencyRate {
  id           String   @id @default(uuid())
  fromCurrency String // USD
  toCurrency   String // IDR
  rate         Decimal  @db.Decimal(15, 6) // Raw rate from API
  sellRate     Decimal  @db.Decimal(15, 6) // Rate with margin for selling
  buyRate      Decimal  @db.Decimal(15, 6) // Rate with margin for buying
  margin       Decimal  @default(0.02) @db.Decimal(5, 4) // 2% margin default
  source       String // API source (exchangerate-api, fixer, etc)
  isActive     Boolean  @default(true)
  date         DateTime @db.Date // Date for this rate
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  @@unique([fromCurrency, toCurrency, date])
  @@index([date])
  @@index([isActive])
}

model Payment {
  id              String    @id @default(uuid())
  orderId         String    @unique
  externalId      String    @unique
  xenditInvoiceId String?   @unique
  xenditPaymentId String?   @unique // For eWallet charges and other payment types
  amount          Decimal   @db.Decimal(15, 2)
  currency        String    @default("IDR")
  status          String    @default("PENDING") // PENDING, PAID, EXPIRED, FAILED
  invoiceUrl      String?   @db.Text
  expiryDate      DateTime?
  expiredAt       DateTime? // Alternative field name for expiry
  paidAt          DateTime?
  paidAmount      Decimal?  @db.Decimal(15, 2)
  paymentMethod   String?
  paymentChannel  String? // For eWallet type (OVO, DANA, etc.)
  webhookData     Json? // Store webhook payload
  order           Order     @relation(fields: [orderId], references: [id], onDelete: Cascade)
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  @@index([orderId])
  @@index([status])
  @@index([xenditInvoiceId])
  @@index([xenditPaymentId])
}

model PaymentMethod {
  id                 String   @id @default(uuid())
  name               String
  description        String?
  type               String // invoice, credit_card, virtual_account, ewallet, qr_code, retail_outlet
  currency           String // USD, IDR
  isActive           Boolean  @default(true)
  isRecommended      Boolean  @default(false)
  icon               String?
  processingFee      Decimal  @db.Decimal(15, 2)
  processingFeeType  String // fixed, percentage
  minAmount          Decimal  @db.Decimal(15, 2)
  maxAmount          Decimal  @db.Decimal(15, 2)
  supportedCountries Json // Array of country codes
  features           Json // Array of features
  xenditConfig       Json // Xendit-specific configuration
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt

  @@unique([name, currency])
  @@index([type])
  @@index([currency])
  @@index([isActive])
}

model ExchangeRate {
  id           String   @id @default(uuid())
  fromCurrency String
  toCurrency   String
  rate         Decimal  @db.Decimal(20, 8)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  @@unique([fromCurrency, toCurrency])
  @@index([fromCurrency, toCurrency])
  @@index([updatedAt])
}
